{"name": "RSGlider API DevContainer", "dockerComposeFile": "docker-compose.yml", "service": "api", "workspaceFolder": "/app", "forwardPorts": [3000, 9229, 9000], "postCreateCommand": "npm install", "customizations": {"vscode": {"settings": {"terminal.integrated.shell.linux": "/bin/sh"}, "extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "ms-azuretools.vscode-docker", "Prisma.prisma"]}}}