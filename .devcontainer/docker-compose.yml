version: '3.8'
services:
  api:
    build:
      context: ..
      dockerfile: Dockerfile
    env_file:
      - ../.env
    ports:
      - 3000:3000
      - 9229:9229
    depends_on:
      - inbucket
    volumes:
      - ..:/app:cached
  inbucket:
    image: inbucket/inbucket:latest
    ports:
      - 9000:9000
      - 2501:2500
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000"]
      interval: 10s
      timeout: 5s
      retries: 5 