import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaClient } from '@prisma/client';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';

export class TestUtils {
  private static app: INestApplication;
  private static prisma: PrismaClient;

  static async getApp(): Promise<INestApplication> {
    if (!this.app) {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      this.app = moduleFixture.createNestApplication();
      await this.app.init();
    }
    return this.app;
  }

  static async getPrisma(): Promise<PrismaClient> {
    if (!this.prisma) {
      this.prisma = new PrismaClient();
    }
    return this.prisma;
  }

  static async cleanupDatabase() {
    const prisma = await this.getPrisma();
    // Add tables to clean up in reverse order of dependencies
    const tables = [
      'sessions',
      'devices',
      'user_roles',
      'role_permissions',
      'permissions',
      'roles',
      'users',
    ];

    for (const table of tables) {
      await prisma.$executeRawUnsafe(`TRUNCATE TABLE "${table}" CASCADE;`);
    }
  }

  static async createTestUser(email: string = '<EMAIL>', password: string = 'Test123!') {
    const app = await this.getApp();
    const response = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email,
        password,
        firstName: 'Test',
        lastName: 'User',
      });
    return response.body;
  }

  static async loginUser(email: string, password: string) {
    const app = await this.getApp();
    const response = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email, password });
    return response.body;
  }

  static async createAdminUser(email: string = '<EMAIL>', password: string = 'Admin123!') {
    const user = await this.createTestUser(email, password);
    const prisma = await this.getPrisma();
    
    // Create admin role if it doesn't exist
    const adminRole = await prisma.role.upsert({
      where: { name: 'admin' },
      update: {},
      create: { name: 'admin', description: 'Administrator role' },
    });

    // Assign admin role to user
    await prisma.userRole.create({
      data: {
        userId: user.id,
        roleId: adminRole.id,
      },
    });

    return user;
  }

  static async closeApp() {
    if (this.app) {
      await this.app.close();
    }
    if (this.prisma) {
      await this.prisma.$disconnect();
    }
  }
} 