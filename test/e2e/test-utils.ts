import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { eq, sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { roles, userRoles } from '../../src/database/schema';

export class TestUtils {
  private static apps: Map<string, INestApplication> = new Map();
  private static dbs: Map<string, PostgresJsDatabase<typeof import('../../src/database/schema')>> = new Map();

  private static getWorkerId(): string {
    // Use Jest worker ID or process ID to create unique test environments
    return process.env.JEST_WORKER_ID || process.pid.toString();
  }

  static async getApp(): Promise<INestApplication> {
    const workerId = this.getWorkerId();

    if (!this.apps.has(workerId)) {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      const app = moduleFixture.createNestApplication();
      await app.init();
      this.apps.set(workerId, app);
    }
    return this.apps.get(workerId)!;
  }

  static async getDb(): Promise<PostgresJsDatabase<typeof import('../../src/database/schema')>> {
    const workerId = this.getWorkerId();

    if (!this.dbs.has(workerId)) {
      const app = await this.getApp();
      const db = app.get('DB');
      this.dbs.set(workerId, db);
    }
    return this.dbs.get(workerId)!;
  }

  static async cleanupDatabase() {
    const db = await this.getDb();

    try {
      // Use a single transaction to delete all test data efficiently
      await db.transaction(async (tx) => {
        // Delete in reverse dependency order
        await tx.execute(sql`DELETE FROM user_sessions`);
        await tx.execute(sql`DELETE FROM devices`);
        await tx.execute(sql`DELETE FROM user_roles`);
        await tx.execute(sql`DELETE FROM role_permissions`);
        await tx.execute(sql`DELETE FROM permissions WHERE is_system_permission = false`);
        await tx.execute(sql`DELETE FROM roles WHERE is_system_role = false`);
        await tx.execute(sql`DELETE FROM users`);
        await tx.execute(sql`DELETE FROM refresh_tokens`);
      });
    } catch (error) {
      // If transaction fails, fall back to individual deletes
      console.warn('Transaction cleanup failed, falling back to individual deletes:', error);
      const tables = ['user_sessions', 'devices', 'user_roles', 'role_permissions', 'refresh_tokens', 'users'];
      for (const table of tables) {
        try {
          await db.execute(sql.raw(`DELETE FROM "${table}"`));
        } catch (e) {
          console.warn(`Failed to clean table ${table}:`, e);
        }
      }
    }
  }

  static async createTestUser(email: string = '<EMAIL>', password: string = 'Test123!') {
    const app = await this.getApp();
    const response = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email,
        password,
        firstName: 'Test',
        lastName: 'User',
      });
    return response.body;
  }

  static async loginUser(email: string, password: string) {
    const app = await this.getApp();
    const response = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email, password });
    return response.body;
  }

  static async createAdminUser(email: string = '<EMAIL>', password: string = 'Admin123!') {
    const user = await this.createTestUser(email, password);
    const db = await this.getDb();

    // Create admin role if it doesn't exist
    let adminRole = await db.select().from(roles).where(eq(roles.name, 'admin')).limit(1);
    if (adminRole.length === 0) {
      const [newRole] = await db.insert(roles).values({
        name: 'admin',
        description: 'Administrator role',
      }).returning();
      adminRole = [newRole];
    }

    // Assign admin role to user
    await db.insert(userRoles).values({
      userId: user.id,
      roleId: adminRole[0].id,
    });

    return user;
  }

  static async closeApp() {
    const workerId = this.getWorkerId();
    const app = this.apps.get(workerId);

    if (app) {
      await app.close();
      this.apps.delete(workerId);
      this.dbs.delete(workerId);
    }
    // Drizzle connections are managed by the app, no need to manually disconnect
  }

  static async closeAllApps() {
    // Close all apps for cleanup
    for (const [workerId, app] of this.apps.entries()) {
      await app.close();
    }
    this.apps.clear();
    this.dbs.clear();
  }
}