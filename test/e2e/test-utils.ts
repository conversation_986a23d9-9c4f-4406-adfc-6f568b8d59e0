import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { eq, sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { roles, userRoles } from '../../src/database/schema';

export class TestUtils {
  private static app: INestApplication;
  private static db: PostgresJsDatabase<typeof import('../../src/database/schema')>;

  static async getApp(): Promise<INestApplication> {
    if (!this.app) {
      const moduleFixture: TestingModule = await Test.createTestingModule({
        imports: [AppModule],
      }).compile();

      this.app = moduleFixture.createNestApplication();
      await this.app.init();
    }
    return this.app;
  }

  static async getDb(): Promise<PostgresJsDatabase<typeof import('../../src/database/schema')>> {
    if (!this.db) {
      const app = await this.getApp();
      this.db = app.get('DB');
    }
    return this.db;
  }

  static async cleanupDatabase() {
    const db = await this.getDb();
    // Add tables to clean up in reverse order of dependencies
    const tables = [
      'user_sessions',
      'devices',
      'user_roles',
      'role_permissions',
      'permissions',
      'roles',
      'users',
    ];

    for (const table of tables) {
      await db.execute(sql.raw(`TRUNCATE TABLE "${table}" CASCADE;`));
    }
  }

  static async createTestUser(email: string = '<EMAIL>', password: string = 'Test123!') {
    const app = await this.getApp();
    const response = await request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email,
        password,
        firstName: 'Test',
        lastName: 'User',
      });
    return response.body;
  }

  static async loginUser(email: string, password: string) {
    const app = await this.getApp();
    const response = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email, password });
    return response.body;
  }

  static async createAdminUser(email: string = '<EMAIL>', password: string = 'Admin123!') {
    const user = await this.createTestUser(email, password);
    const db = await this.getDb();

    // Create admin role if it doesn't exist
    let adminRole = await db.select().from(roles).where(eq(roles.name, 'admin')).limit(1);
    if (adminRole.length === 0) {
      const [newRole] = await db.insert(roles).values({
        name: 'admin',
        description: 'Administrator role',
      }).returning();
      adminRole = [newRole];
    }

    // Assign admin role to user
    await db.insert(userRoles).values({
      userId: user.id,
      roleId: adminRole[0].id,
    });

    return user;
  }

  static async closeApp() {
    if (this.app) {
      await this.app.close();
    }
    // Drizzle connections are managed by the app, no need to manually disconnect
  }
}