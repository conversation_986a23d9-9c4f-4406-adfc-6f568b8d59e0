import * as request from 'supertest';
import { TestUtils } from './test-utils';

describe('User Profile Management (e2e)', () => {
  let app;
  let accessToken: string;
  let userId: string;

  beforeAll(async () => {
    app = await TestUtils.getApp();
  });

  beforeEach(async () => {
    await TestUtils.cleanupDatabase();
    const user = await TestUtils.createTestUser();
    const loginResponse = await TestUtils.loginUser('<EMAIL>', 'Test123!');
    accessToken = loginResponse.accessToken;
    userId = user.id;
  });

  afterAll(async () => {
    await TestUtils.closeApp();
  });

  describe('GET /users/me', () => {
    it('should get current user profile', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', userId);
      expect(response.body).toHaveProperty('email', '<EMAIL>');
      expect(response.body).toHaveProperty('firstName', 'Test');
      expect(response.body).toHaveProperty('lastName', 'User');
    });

    it('should not get profile without token', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/me');

      expect(response.status).toBe(401);
    });
  });

  describe('PUT /users/me', () => {
    it('should update user profile', async () => {
      const response = await request(app.getHttpServer())
        .put('/users/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          firstName: 'Updated',
          lastName: 'Name',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('firstName', 'Updated');
      expect(response.body).toHaveProperty('lastName', 'Name');
    });

    it('should not update with invalid data', async () => {
      const response = await request(app.getHttpServer())
        .put('/users/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          firstName: '', // Empty string should be rejected
        });

      expect(response.status).toBe(400);
    });
  });

  describe('2FA Management', () => {
    describe('POST /users/me/2fa/setup', () => {
      it('should generate 2FA secret and QR code', async () => {
        const response = await request(app.getHttpServer())
          .post('/users/me/2fa/setup')
          .set('Authorization', `Bearer ${accessToken}`);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('secret');
        expect(response.body).toHaveProperty('qrCode');
      });
    });

    describe('POST /users/me/2fa/verify-setup', () => {
      let secret: string;

      beforeEach(async () => {
        const setupResponse = await request(app.getHttpServer())
          .post('/users/me/2fa/setup')
          .set('Authorization', `Bearer ${accessToken}`);
        secret = setupResponse.body.secret;
      });

      it('should verify and enable 2FA with valid code', async () => {
        // Note: In a real test, you'd need to generate a valid TOTP code
        // This is just a placeholder - you'll need to implement proper TOTP code generation
        const response = await request(app.getHttpServer())
          .post('/users/me/2fa/verify-setup')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: '123456', // This should be a valid TOTP code
          });

        expect(response.status).toBe(200);
      });
    });

    describe('POST /users/me/2fa/disable', () => {
      it('should disable 2FA with valid code', async () => {
        const response = await request(app.getHttpServer())
          .post('/users/me/2fa/disable')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            code: '123456', // This should be a valid TOTP code
          });

        expect(response.status).toBe(200);
      });
    });
  });

  describe('Device Management', () => {
    describe('GET /users/me/devices', () => {
      it('should list registered devices', async () => {
        const response = await request(app.getHttpServer())
          .get('/users/me/devices')
          .set('Authorization', `Bearer ${accessToken}`);

        expect(response.status).toBe(200);
        expect(Array.isArray(response.body)).toBe(true);
      });
    });

    describe('POST /users/me/devices', () => {
      it('should register new device', async () => {
        const response = await request(app.getHttpServer())
          .post('/users/me/devices')
          .set('Authorization', `Bearer ${accessToken}`)
          .send({
            name: 'Test Device',
            type: 'mobile',
          });

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('id');
        expect(response.body).toHaveProperty('name', 'Test Device');
      });
    });
  });

  describe('Session Management', () => {
    describe('GET /users/me/sessions', () => {
      it('should list active sessions', async () => {
        const response = await request(app.getHttpServer())
          .get('/users/me/sessions')
          .set('Authorization', `Bearer ${accessToken}`);

        expect(response.status).toBe(200);
        expect(Array.isArray(response.body)).toBe(true);
      });
    });

    describe('DELETE /users/me/sessions', () => {
      it('should revoke all sessions except current', async () => {
        const response = await request(app.getHttpServer())
          .delete('/users/me/sessions')
          .set('Authorization', `Bearer ${accessToken}`);

        expect(response.status).toBe(200);
      });
    });
  });
}); 