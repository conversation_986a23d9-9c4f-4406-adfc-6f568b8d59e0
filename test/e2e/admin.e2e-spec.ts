import * as request from 'supertest';
import { TestUtils } from './test-utils';

describe('Admin API (e2e)', () => {
  let app;
  let adminToken: string;
  let regularUserToken: string;
  let adminId: string;
  let regularUserId: string;

  beforeAll(async () => {
    app = await TestUtils.getApp();
  });

  beforeEach(async () => {
    await TestUtils.cleanupDatabase();
    
    // Create admin user
    const adminUser = await TestUtils.createAdminUser();
    const adminLogin = await TestUtils.loginUser('<EMAIL>', 'Admin123!');
    adminToken = adminLogin.accessToken;
    adminId = adminUser.id;

    // Create regular user
    const regularUser = await TestUtils.createTestUser('<EMAIL>', 'User123!');
    const userLogin = await TestUtils.loginUser('<EMAIL>', 'User123!');
    regularUserToken = userLogin.accessToken;
    regularUserId = regularUser.id;
  });

  afterAll(async () => {
    await TestUtils.closeApp();
  });

  describe('User Management', () => {
    describe('GET /admin/users', () => {
      it('should list all users (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .get('/admin/users')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(Array.isArray(response.body)).toBe(true);
        expect(response.body.length).toBeGreaterThan(0);
      });

      it('should not allow regular users to list all users', async () => {
        const response = await request(app.getHttpServer())
          .get('/admin/users')
          .set('Authorization', `Bearer ${regularUserToken}`);

        expect(response.status).toBe(403);
      });
    });

    describe('POST /admin/users', () => {
      it('should create new user (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .post('/admin/users')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            email: '<EMAIL>',
            password: 'NewUser123!',
            firstName: 'New',
            lastName: 'User',
          });

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('id');
        expect(response.body.email).toBe('<EMAIL>');
      });
    });

    describe('GET /admin/users/:userId', () => {
      it('should get user details (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .get(`/admin/users/${regularUserId}`)
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('id', regularUserId);
      });
    });

    describe('PUT /admin/users/:userId', () => {
      it('should update user (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .put(`/admin/users/${regularUserId}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            firstName: 'Updated',
            lastName: 'Name',
          });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('firstName', 'Updated');
      });
    });

    describe('DELETE /admin/users/:userId', () => {
      it('should delete user (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .delete(`/admin/users/${regularUserId}`)
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);

        // Verify user is deleted
        const getResponse = await request(app.getHttpServer())
          .get(`/admin/users/${regularUserId}`)
          .set('Authorization', `Bearer ${adminToken}`);

        expect(getResponse.status).toBe(404);
      });
    });
  });

  describe('Role Management', () => {
    describe('GET /admin/roles', () => {
      it('should list all roles (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .get('/admin/roles')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(Array.isArray(response.body)).toBe(true);
      });
    });

    describe('POST /admin/roles', () => {
      it('should create new role (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .post('/admin/roles')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            name: 'test-role',
            description: 'Test Role',
          });

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('id');
        expect(response.body.name).toBe('test-role');
      });
    });
  });

  describe('Permission Management', () => {
    describe('GET /admin/permissions', () => {
      it('should list all permissions (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .get('/admin/permissions')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(Array.isArray(response.body)).toBe(true);
      });
    });

    describe('POST /admin/permissions', () => {
      it('should create new permission (admin only)', async () => {
        const response = await request(app.getHttpServer())
          .post('/admin/permissions')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            name: 'test:permission',
            description: 'Test Permission',
          });

        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('id');
        expect(response.body.name).toBe('test:permission');
      });
    });
  });
}); 