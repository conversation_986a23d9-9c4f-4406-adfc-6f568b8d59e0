import { TestUtils } from './e2e/test-utils';

// Increase timeout for e2e tests
jest.setTimeout(30000);

// Global setup
beforeAll(async () => {
  // Any global setup can go here
});

// Clean up after each test to prevent interference
afterEach(async () => {
  try {
    await TestUtils.cleanupDatabase();
  } catch (error) {
    console.warn('Failed to cleanup database after test:', error);
  }
});

// Global teardown
afterAll(async () => {
  try {
    await TestUtils.closeApp();
  } catch (error) {
    console.warn('Failed to close app:', error);
  }
});